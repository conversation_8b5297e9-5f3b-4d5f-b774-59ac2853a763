/**
 * Confirmation dialog component for order actions
 */

import React from 'react';
import { 
  View, 
  StyleSheet, 
  Modal, 
  TouchableOpacity, 
  TouchableWithoutFeedback,
  Dimensions 
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { formatCurrency } from '@/utils/orderUtils';

export interface ConfirmationDialogProps {
  visible: boolean;
  title: string;
  message: string;
  type?: 'cancel' | 'modify' | 'warning' | 'info';
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
  details?: {
    refundAmount?: number;
    newTotal?: number;
    processingTime?: string;
  };
}

export function ConfirmationDialog({
  visible,
  title,
  message,
  type = 'info',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  loading = false,
  details,
}: ConfirmationDialogProps) {
  const backgroundColor = useThemeColor({}, 'background');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');
  const mutedColor = useThemeColor({}, 'muted');
  const tintColor = useThemeColor({}, 'tint');
  const errorColor = useThemeColor({}, 'error');
  const warningColor = useThemeColor({}, 'warning');

  const getTypeConfig = () => {
    switch (type) {
      case 'cancel':
        return {
          icon: 'xmark.circle',
          iconColor: errorColor,
          confirmVariant: 'danger' as const,
        };
      case 'modify':
        return {
          icon: 'pencil.circle',
          iconColor: tintColor,
          confirmVariant: 'primary' as const,
        };
      case 'warning':
        return {
          icon: 'exclamationmark.triangle',
          iconColor: warningColor,
          confirmVariant: 'primary' as const,
        };
      default:
        return {
          icon: 'info.circle',
          iconColor: tintColor,
          confirmVariant: 'primary' as const,
        };
    }
  };

  const typeConfig = getTypeConfig();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <TouchableWithoutFeedback onPress={onCancel}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <ThemedView style={[
              styles.dialog,
              { 
                backgroundColor: cardBackground,
                borderColor,
              }
            ]}>
              {/* Header */}
              <View style={styles.header}>
                <View style={styles.iconContainer}>
                  <IconSymbol
                    name={typeConfig.icon as any}
                    size={32}
                    color={typeConfig.iconColor}
                  />
                </View>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={onCancel}
                  accessibilityLabel="Close dialog"
                >
                  <IconSymbol name="xmark" size={20} color={mutedColor} />
                </TouchableOpacity>
              </View>

              {/* Content */}
              <View style={styles.content}>
                <ThemedText type="defaultSemiBold" style={styles.title}>
                  {title}
                </ThemedText>
                <ThemedText style={[styles.message, { color: mutedColor }]}>
                  {message}
                </ThemedText>

                {/* Details */}
                {details && (
                  <View style={[styles.details, { backgroundColor: `${tintColor}08`, borderColor: `${tintColor}20` }]}>
                    {details.refundAmount !== undefined && (
                      <View style={styles.detailRow}>
                        <ThemedText style={styles.detailLabel}>Refund Amount:</ThemedText>
                        <ThemedText type="defaultSemiBold" style={styles.detailValue}>
                          {formatCurrency(details.refundAmount)}
                        </ThemedText>
                      </View>
                    )}
                    
                    {details.newTotal !== undefined && (
                      <View style={styles.detailRow}>
                        <ThemedText style={styles.detailLabel}>New Total:</ThemedText>
                        <ThemedText type="defaultSemiBold" style={styles.detailValue}>
                          {formatCurrency(details.newTotal)}
                        </ThemedText>
                      </View>
                    )}
                    
                    {details.processingTime && (
                      <View style={styles.detailRow}>
                        <ThemedText style={styles.detailLabel}>Processing Time:</ThemedText>
                        <ThemedText style={[styles.detailValue, { color: mutedColor }]}>
                          {details.processingTime}
                        </ThemedText>
                      </View>
                    )}
                  </View>
                )}
              </View>

              {/* Actions */}
              <View style={styles.actions}>
                <Button
                  title={cancelText}
                  variant="outline"
                  onPress={onCancel}
                  style={styles.actionButton}
                  disabled={loading}
                />
                <Button
                  title={confirmText}
                  variant={typeConfig.confirmVariant}
                  onPress={onConfirm}
                  style={styles.actionButton}
                  loading={loading}
                />
              </View>
            </ThemedView>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const { width: screenWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dialog: {
    width: Math.min(screenWidth - 40, 400),
    borderRadius: 16,
    borderWidth: 1,
    padding: 0,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 0,
  },
  iconContainer: {
    flex: 1,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 8,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 20,
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 16,
  },
  details: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
  },
  actions: {
    flexDirection: 'row',
    padding: 20,
    paddingTop: 0,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});
