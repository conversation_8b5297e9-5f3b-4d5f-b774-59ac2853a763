/**
 * Order items list component with modification support
 */

import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { OrderItem, OrderItemModification, OrderItemModificationType } from '@/types/order';
import { formatCurrency } from '@/utils/orderUtils';

export interface OrderItemsListProps {
  items: OrderItem[];
  editable?: boolean;
  onItemModify?: (modification: OrderItemModification) => void;
  modifiedItems?: Set<string>;
}

export function OrderItemsList({ 
  items, 
  editable = false, 
  onItemModify,
  modifiedItems = new Set()
}: OrderItemsListProps) {
  const borderColor = useThemeColor({}, 'border');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const mutedColor = useThemeColor({}, 'muted');
  const tintColor = useThemeColor({}, 'tint');
  const errorColor = useThemeColor({}, 'error');

  const handleQuantityChange = (item: OrderItem, newQuantity: number) => {
    if (!onItemModify) return;

    if (newQuantity === 0) {
      onItemModify({
        type: OrderItemModificationType.REMOVE,
        itemId: item.id,
      });
    } else {
      onItemModify({
        type: OrderItemModificationType.UPDATE_QUANTITY,
        itemId: item.id,
        newQuantity,
      });
    }
  };

  const handleRemoveItem = (item: OrderItem) => {
    if (!onItemModify) return;

    onItemModify({
      type: OrderItemModificationType.REMOVE,
      itemId: item.id,
    });
  };

  const calculateItemTotal = (item: OrderItem): number => {
    const basePrice = item.price * item.quantity;
    const customizationPrice = (item.customizations || []).reduce(
      (sum, custom) => sum + (custom.price * item.quantity), 0
    );
    return basePrice + customizationPrice;
  };

  return (
    <ThemedView style={[styles.container, { backgroundColor: cardBackground, borderColor }]}>
      <ThemedText type="defaultSemiBold" style={styles.title}>
        Order Items
      </ThemedText>

      {items.map((item, index) => {
        const isModified = modifiedItems.has(item.id);
        const itemTotal = calculateItemTotal(item);

        return (
          <View key={item.id}>
            <View style={[
              styles.itemContainer,
              isModified && { backgroundColor: `${tintColor}08` }
            ]}>
              {/* Item Image */}
              {item.image && (
                <Image source={{ uri: item.image }} style={styles.itemImage} />
              )}

              {/* Item Details */}
              <View style={styles.itemDetails}>
                <View style={styles.itemHeader}>
                  <ThemedText type="defaultSemiBold" style={styles.itemName}>
                    {item.name}
                  </ThemedText>
                  {isModified && (
                    <View style={[styles.modifiedBadge, { backgroundColor: tintColor }]}>
                      <ThemedText style={styles.modifiedText}>Modified</ThemedText>
                    </View>
                  )}
                </View>

                {item.description && (
                  <ThemedText style={[styles.itemDescription, { color: mutedColor }]}>
                    {item.description}
                  </ThemedText>
                )}

                {/* Customizations */}
                {item.customizations && item.customizations.length > 0 && (
                  <View style={styles.customizations}>
                    {item.customizations.map((custom) => (
                      <View key={custom.id} style={styles.customizationRow}>
                        <ThemedText style={[styles.customizationText, { color: mutedColor }]}>
                          {custom.name}: {custom.value}
                        </ThemedText>
                        {custom.price > 0 && (
                          <ThemedText style={[styles.customizationPrice, { color: mutedColor }]}>
                            +{formatCurrency(custom.price)}
                          </ThemedText>
                        )}
                      </View>
                    ))}
                  </View>
                )}

                {/* Price and Quantity */}
                <View style={styles.itemFooter}>
                  <View style={styles.priceSection}>
                    <ThemedText style={styles.itemPrice}>
                      {formatCurrency(item.price)}
                    </ThemedText>
                    <ThemedText type="defaultSemiBold" style={styles.itemTotal}>
                      {formatCurrency(itemTotal)}
                    </ThemedText>
                  </View>

                  {editable ? (
                    <View style={styles.quantityControls}>
                      <TouchableOpacity
                        style={[styles.quantityButton, { borderColor }]}
                        onPress={() => handleQuantityChange(item, item.quantity - 1)}
                        accessibilityLabel={`Decrease quantity of ${item.name}`}
                      >
                        <IconSymbol name="minus" size={16} color={tintColor} />
                      </TouchableOpacity>
                      
                      <ThemedText style={styles.quantityText}>
                        {item.quantity}
                      </ThemedText>
                      
                      <TouchableOpacity
                        style={[styles.quantityButton, { borderColor }]}
                        onPress={() => handleQuantityChange(item, item.quantity + 1)}
                        accessibilityLabel={`Increase quantity of ${item.name}`}
                      >
                        <IconSymbol name="plus" size={16} color={tintColor} />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.removeButton, { backgroundColor: `${errorColor}15` }]}
                        onPress={() => handleRemoveItem(item)}
                        accessibilityLabel={`Remove ${item.name} from order`}
                      >
                        <IconSymbol name="trash" size={16} color={errorColor} />
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <View style={styles.quantityDisplay}>
                      <ThemedText style={[styles.quantityLabel, { color: mutedColor }]}>
                        Qty: {item.quantity}
                      </ThemedText>
                    </View>
                  )}
                </View>

                {/* Availability Warning */}
                {item.available === false && (
                  <View style={[styles.unavailableWarning, { backgroundColor: `${errorColor}15` }]}>
                    <IconSymbol name="exclamationmark.triangle" size={14} color={errorColor} />
                    <ThemedText style={[styles.unavailableText, { color: errorColor }]}>
                      This item is currently unavailable
                    </ThemedText>
                  </View>
                )}
              </View>
            </View>

            {index < items.length - 1 && (
              <View style={[styles.separator, { backgroundColor: borderColor }]} />
            )}
          </View>
        );
      })}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    marginBottom: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    fontSize: 16,
    flex: 1,
    marginRight: 8,
  },
  modifiedBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  modifiedText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  itemDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 8,
  },
  customizations: {
    marginBottom: 8,
  },
  customizationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  customizationText: {
    fontSize: 12,
    flex: 1,
  },
  customizationPrice: {
    fontSize: 12,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceSection: {
    flex: 1,
  },
  itemPrice: {
    fontSize: 14,
    marginBottom: 2,
  },
  itemTotal: {
    fontSize: 16,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 12,
    minWidth: 24,
    textAlign: 'center',
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  quantityDisplay: {
    alignItems: 'flex-end',
  },
  quantityLabel: {
    fontSize: 14,
  },
  unavailableWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
  },
  unavailableText: {
    fontSize: 12,
    marginLeft: 4,
    flex: 1,
  },
  separator: {
    height: 1,
    marginVertical: 8,
  },
});
