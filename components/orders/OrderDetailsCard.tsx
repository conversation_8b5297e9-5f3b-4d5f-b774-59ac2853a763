/**
 * Order details card component
 */

import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Order } from '@/types/order';
import { formatCurrency, formatTimeRemaining } from '@/utils/orderUtils';
import { OrderStatusBadge } from './OrderStatusBadge';

export interface OrderDetailsCardProps {
  order: Order;
  showTimeConstraints?: boolean;
  timeRemaining?: {
    cancellation: number;
    modification: number;
  };
}

export function OrderDetailsCard({ 
  order, 
  showTimeConstraints = false,
  timeRemaining 
}: OrderDetailsCardProps) {
  const borderColor = useThemeColor({}, 'border');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const mutedColor = useThemeColor({}, 'muted');
  const tintColor = useThemeColor({}, 'tint');

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    }).format(date);
  };

  return (
    <ThemedView style={[styles.container, { backgroundColor: cardBackground, borderColor }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          {order.restaurantImage && (
            <Image 
              source={{ uri: order.restaurantImage }} 
              style={styles.restaurantImage}
            />
          )}
          <View style={styles.headerInfo}>
            <ThemedText type="defaultSemiBold" style={styles.restaurantName}>
              {order.restaurantName}
            </ThemedText>
            <ThemedText style={[styles.orderId, { color: mutedColor }]}>
              Order #{order.id}
            </ThemedText>
          </View>
        </View>
        <OrderStatusBadge status={order.status} />
      </View>

      {/* Order Info */}
      <View style={styles.infoSection}>
        <View style={styles.infoRow}>
          <IconSymbol name="clock" size={16} color={mutedColor} />
          <ThemedText style={[styles.infoText, { color: mutedColor }]}>
            Placed: {formatDate(order.placedAt)}
          </ThemedText>
        </View>
        
        {order.estimatedDeliveryTime && (
          <View style={styles.infoRow}>
            <IconSymbol name="car" size={16} color={mutedColor} />
            <ThemedText style={[styles.infoText, { color: mutedColor }]}>
              Estimated: {formatDate(order.estimatedDeliveryTime)}
            </ThemedText>
          </View>
        )}

        <View style={styles.infoRow}>
          <IconSymbol name="location" size={16} color={mutedColor} />
          <ThemedText style={[styles.infoText, { color: mutedColor }]} numberOfLines={2}>
            {order.deliveryAddress}
          </ThemedText>
        </View>
      </View>

      {/* Time Constraints */}
      {showTimeConstraints && timeRemaining && (
        <View style={[styles.timeConstraints, { backgroundColor: `${tintColor}10`, borderColor: `${tintColor}30` }]}>
          <IconSymbol name="exclamationmark.triangle" size={16} color={tintColor} />
          <View style={styles.timeConstraintsText}>
            <ThemedText type="defaultSemiBold" style={styles.timeConstraintsTitle}>
              Time Limits
            </ThemedText>
            <ThemedText style={[styles.timeConstraintsDetail, { color: mutedColor }]}>
              Cancel: {formatTimeRemaining(timeRemaining.cancellation)} remaining
            </ThemedText>
            <ThemedText style={[styles.timeConstraintsDetail, { color: mutedColor }]}>
              Modify: {formatTimeRemaining(timeRemaining.modification)} remaining
            </ThemedText>
          </View>
        </View>
      )}

      {/* Order Total */}
      <View style={[styles.totalSection, { borderTopColor: borderColor }]}>
        <View style={styles.totalRow}>
          <ThemedText style={styles.totalLabel}>Order Total</ThemedText>
          <ThemedText type="defaultSemiBold" style={styles.totalAmount}>
            {formatCurrency(order.pricing.total)}
          </ThemedText>
        </View>
        <ThemedText style={[styles.itemCount, { color: mutedColor }]}>
          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
        </ThemedText>
      </View>

      {/* Customer Notes */}
      {order.customerNotes && (
        <View style={[styles.notesSection, { borderTopColor: borderColor }]}>
          <ThemedText type="defaultSemiBold" style={styles.notesTitle}>
            Special Instructions
          </ThemedText>
          <ThemedText style={[styles.notesText, { color: mutedColor }]}>
            {order.customerNotes}
          </ThemedText>
        </View>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 12,
  },
  restaurantImage: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 18,
    marginBottom: 4,
  },
  orderId: {
    fontSize: 14,
  },
  infoSection: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  timeConstraints: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  timeConstraintsText: {
    marginLeft: 8,
    flex: 1,
  },
  timeConstraintsTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  timeConstraintsDetail: {
    fontSize: 12,
    lineHeight: 16,
  },
  totalSection: {
    borderTopWidth: 1,
    paddingTop: 16,
    marginBottom: 16,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  totalLabel: {
    fontSize: 16,
  },
  totalAmount: {
    fontSize: 18,
  },
  itemCount: {
    fontSize: 14,
  },
  notesSection: {
    borderTopWidth: 1,
    paddingTop: 16,
  },
  notesTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
