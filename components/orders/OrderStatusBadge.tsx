/**
 * Order status badge component
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { OrderStatus } from '@/types/order';
import { getOrderStatusInfo } from '@/utils/orderUtils';

export interface OrderStatusBadgeProps {
  status: OrderStatus;
  size?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
}

export function OrderStatusBadge({ 
  status, 
  size = 'medium', 
  showIcon = true 
}: OrderStatusBadgeProps) {
  const statusInfo = getOrderStatusInfo(status);

  const sizeStyles = {
    small: {
      container: styles.smallContainer,
      text: styles.smallText,
      icon: 12,
    },
    medium: {
      container: styles.mediumContainer,
      text: styles.mediumText,
      icon: 16,
    },
    large: {
      container: styles.largeContainer,
      text: styles.largeText,
      icon: 20,
    },
  };

  const currentSize = sizeStyles[size];

  return (
    <View 
      style={[
        styles.container,
        currentSize.container,
        { 
          backgroundColor: `${statusInfo.color}15`,
          borderColor: `${statusInfo.color}40`,
        }
      ]}
    >
      {showIcon && (
        <IconSymbol
          name={statusInfo.icon as any}
          size={currentSize.icon}
          color={statusInfo.color}
          style={styles.icon}
        />
      )}
      <ThemedText 
        style={[
          currentSize.text,
          { color: statusInfo.color }
        ]}
        type="defaultSemiBold"
      >
        {statusInfo.label}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  smallContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  mediumContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  largeContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  icon: {
    marginRight: 4,
  },
  smallText: {
    fontSize: 12,
    lineHeight: 16,
  },
  mediumText: {
    fontSize: 14,
    lineHeight: 20,
  },
  largeText: {
    fontSize: 16,
    lineHeight: 24,
  },
});
