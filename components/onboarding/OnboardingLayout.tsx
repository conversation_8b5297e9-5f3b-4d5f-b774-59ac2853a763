/**
 * Main onboarding layout component with progress indicator and navigation
 */

import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming,
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useThemeColor } from '@/hooks/useThemeColor';



export interface OnboardingLayoutProps {
  children: React.ReactNode;
  showSkip?: boolean;
  showBack?: boolean;
  showNext?: boolean;
  nextButtonText?: string;
  onSkip?: () => void;
  onNext?: () => void;
  onBack?: () => void;
  customActions?: React.ReactNode;
}

export function OnboardingLayout({
  children,
  showSkip = true,
  showBack = true,
  showNext = true,
  nextButtonText = 'Next',
  onSkip,
  onNext,
  onBack,
  customActions,
}: OnboardingLayoutProps) {
  const {
    currentStep,
    isFirstStep,
    isLastStep,
    progress,
    nextStep,
    previousStep,
    skipStep,
    skipTutorial,
  } = useOnboarding();

  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const tintColor = useThemeColor({}, 'tint');
  const mutedColor = useThemeColor({}, 'muted');

  // Animated values
  const progressValue = useSharedValue(0);
  const fadeValue = useSharedValue(1);

  // Update progress animation
  React.useEffect(() => {
    progressValue.value = withSpring(progress / 100, {
      damping: 15,
      stiffness: 150,
    });
  }, [progress, progressValue]);

  // Progress bar animated style
  const progressBarStyle = useAnimatedStyle(() => ({
    width: `${progressValue.value * 100}%`,
  }));

  // Content fade animation
  const contentStyle = useAnimatedStyle(() => ({
    opacity: fadeValue.value,
  }));

  const handleNext = async () => {
    if (onNext) {
      onNext();
    } else {
      fadeValue.value = withTiming(0, { duration: 200 }, () => {
        fadeValue.value = withTiming(1, { duration: 200 });
      });
      await nextStep();
    }
  };

  const handleBack = async () => {
    if (onBack) {
      onBack();
    } else {
      fadeValue.value = withTiming(0, { duration: 200 }, () => {
        fadeValue.value = withTiming(1, { duration: 200 });
      });
      await previousStep();
    }
  };

  const handleSkip = async () => {
    if (onSkip) {
      onSkip();
    } else if (currentStep.skippable) {
      await skipStep();
    } else {
      await skipTutorial();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={['top']}>
      {/* Header with progress and skip */}
      <View style={styles.header}>
        {/* Progress bar */}
        <View style={[styles.progressContainer, { borderColor }]}>
          <Animated.View
            style={[
              styles.progressBar,
              { backgroundColor: tintColor },
              progressBarStyle
            ]}
          />
        </View>

        {/* Skip button */}
        {showSkip && (currentStep.skippable || !isLastStep) && (
          <Button
            title="Skip"
            variant="ghost"
            size="small"
            onPress={handleSkip}
            style={styles.skipButton}
          />
        )}
      </View>

      {/* Step indicator */}
      <View style={styles.stepIndicator}>
        <ThemedText type="defaultSemiBold" style={[styles.stepText, { color: mutedColor }]}>
          Step {currentStep ? currentStep.id : '1'} of {5}
        </ThemedText>
      </View>

      {/* Main content */}
      <Animated.View style={[styles.content, contentStyle]}>
        {children}
      </Animated.View>

      {/* Navigation footer */}
      <View style={[styles.footer, { borderTopColor: borderColor }]}>
        {customActions || (
          <View style={styles.navigationContainer}>
            {/* Back button */}
            {showBack && !isFirstStep && (
              <Button
                title="Back"
                variant="outline"
                size="medium"
                leftIcon={
                  <IconSymbol
                    name="chevron.left"
                    size={16}
                    color={tintColor}
                  />
                }
                onPress={handleBack}
                style={styles.backButton}
              />
            )}

            {/* Spacer */}
            <View style={styles.spacer} />

            {/* Next button */}
            {showNext && (
              <Button
                title={isLastStep ? 'Get Started' : nextButtonText}
                variant="primary"
                size="medium"
                rightIcon={
                  !isLastStep ? (
                    <IconSymbol
                      name="chevron.right"
                      size={16}
                      color="white"
                    />
                  ) : (
                    <IconSymbol
                      name="checkmark"
                      size={16}
                      color="white"
                    />
                  )
                }
                onPress={handleNext}
                style={styles.nextButton}
              />
            )}
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  progressContainer: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    overflow: 'hidden',
    borderWidth: Platform.select({ web: 1, default: 0 }),
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  skipButton: {
    minWidth: 60,
  },
  stepIndicator: {
    paddingHorizontal: 20,
    paddingBottom: 8,
  },
  stepText: {
    fontSize: 14,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  footer: {
    borderTopWidth: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: Platform.select({ ios: 34, default: 16 }),
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  backButton: {
    minWidth: 80,
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    minWidth: 120,
  },
});
