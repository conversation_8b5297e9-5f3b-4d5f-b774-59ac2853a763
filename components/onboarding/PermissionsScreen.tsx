/**
 * Permissions screen for requesting location and notification access
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { usePermissions } from '@/hooks/usePermissions';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { Alert, ScrollView, StyleSheet, View } from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withDelay,
    withSpring,
    withTiming,
} from 'react-native-reanimated';

export interface PermissionsScreenProps {
  onPermissionsGranted?: (permissions: { location: boolean; notifications: boolean }) => void;
  onSkip?: () => void;
}

export function PermissionsScreen({ onPermissionsGranted, onSkip }: PermissionsScreenProps) {
  const tintColor = useThemeColor({}, 'tint');
  const mutedColor = useThemeColor({}, 'muted');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');
  const successColor = useThemeColor({}, 'success');
  const warningColor = useThemeColor({}, 'warning');

  const {
    permissions,
    isLoading,
    error,
    requestLocationPermission,
    requestNotificationPermission,
    requestAllPermissions,
    openSettings,
  } = usePermissions();

  // Animation values
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-30);
  const locationOpacity = useSharedValue(0);
  const locationScale = useSharedValue(0.9);
  const notificationOpacity = useSharedValue(0);
  const notificationScale = useSharedValue(0.9);
  const actionsOpacity = useSharedValue(0);
  const actionsTranslateY = useSharedValue(30);

  // Start animations on mount
  React.useEffect(() => {
    // Header animation
    headerOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    headerTranslateY.value = withDelay(200, withSpring(0, { damping: 15, stiffness: 150 }));

    // Location animation
    locationOpacity.value = withDelay(500, withTiming(1, { duration: 600 }));
    locationScale.value = withDelay(500, withSpring(1, { damping: 15, stiffness: 150 }));

    // Notification animation
    notificationOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    notificationScale.value = withDelay(800, withSpring(1, { damping: 15, stiffness: 150 }));

    // Actions animation
    actionsOpacity.value = withDelay(1100, withTiming(1, { duration: 600 }));
    actionsTranslateY.value = withDelay(1100, withSpring(0, { damping: 15, stiffness: 150 }));
  }, []);

  // Animated styles
  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const locationStyle = useAnimatedStyle(() => ({
    opacity: locationOpacity.value,
    transform: [{ scale: locationScale.value }],
  }));

  const notificationStyle = useAnimatedStyle(() => ({
    opacity: notificationOpacity.value,
    transform: [{ scale: notificationScale.value }],
  }));

  const actionsStyle = useAnimatedStyle(() => ({
    opacity: actionsOpacity.value,
    transform: [{ translateY: actionsTranslateY.value }],
  }));

  const handleLocationPermission = async () => {
    try {
      const granted = await requestLocationPermission();
      if (!granted && !permissions.location.canAskAgain) {
        Alert.alert(
          'Location Permission Required',
          'Location access is needed to show nearby restaurants and enable delivery tracking. Please enable it in Settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: openSettings },
          ]
        );
      }
    } catch (err) {
      console.error('Error requesting location permission:', err);
    }
  };

  const handleNotificationPermission = async () => {
    try {
      const granted = await requestNotificationPermission();
      if (!granted && !permissions.notifications.canAskAgain) {
        Alert.alert(
          'Notification Permission Required',
          'Notifications help you stay updated on order status and special offers. Please enable them in Settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: openSettings },
          ]
        );
      }
    } catch (err) {
      console.error('Error requesting notification permission:', err);
    }
  };

  const handleAllPermissions = async () => {
    try {
      const result = await requestAllPermissions();
      onPermissionsGranted?.(result);
    } catch (err) {
      console.error('Error requesting all permissions:', err);
    }
  };

  const getPermissionStatus = (status: string) => {
    switch (status) {
      case 'granted':
        return { color: successColor, icon: 'checkmark.circle.fill', text: 'Granted' };
      case 'denied':
        return { color: warningColor, icon: 'xmark.circle.fill', text: 'Denied' };
      default:
        return { color: mutedColor, icon: 'questionmark.circle', text: 'Not requested' };
    }
  };

  const locationStatus = getPermissionStatus(permissions.location.status);
  const notificationStatus = getPermissionStatus(permissions.notifications.status);

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <Animated.View style={[styles.header, headerStyle]}>
          <View style={[styles.iconContainer, { backgroundColor: `${tintColor}15` }]}>
            <IconSymbol name="lock.shield" size={48} color={tintColor} />
          </View>
          <ThemedText type="title" style={styles.title}>
            Enable Features
          </ThemedText>
          <ThemedText style={[styles.subtitle, { color: mutedColor }]}>
            Allow location and notifications for the best experience with personalized recommendations and order updates
          </ThemedText>
        </Animated.View>

        {/* Location Permission */}
        <Animated.View style={[styles.permissionCard, { backgroundColor: cardBackground, borderColor }, locationStyle]}>
          <View style={styles.permissionHeader}>
            <View style={[styles.permissionIcon, { backgroundColor: `${tintColor}15` }]}>
              <IconSymbol name="location.fill" size={24} color={tintColor} />
            </View>
            <View style={styles.permissionInfo}>
              <ThemedText type="defaultSemiBold" style={styles.permissionTitle}>
                Location Access
              </ThemedText>
              <View style={styles.permissionStatus}>
                <IconSymbol
                  name={locationStatus.icon as any}
                  size={16}
                  color={locationStatus.color}
                />
                <ThemedText style={[styles.permissionStatusText, { color: locationStatus.color }]}>
                  {locationStatus.text}
                </ThemedText>
              </View>
            </View>
          </View>

          <ThemedText style={[styles.permissionDescription, { color: mutedColor }]}>
            We use your location to:
          </ThemedText>

          <View style={styles.permissionBenefits}>
            <View style={styles.benefitItem}>
              <IconSymbol name="checkmark" size={14} color={successColor} />
              <ThemedText style={styles.benefitText}>Show nearby restaurants</ThemedText>
            </View>
            <View style={styles.benefitItem}>
              <IconSymbol name="checkmark" size={14} color={successColor} />
              <ThemedText style={styles.benefitText}>Accurate delivery tracking</ThemedText>
            </View>
            <View style={styles.benefitItem}>
              <IconSymbol name="checkmark" size={14} color={successColor} />
              <ThemedText style={styles.benefitText}>Faster checkout with saved addresses</ThemedText>
            </View>
          </View>

          {permissions.location.status !== 'granted' && (
            <Button
              title="Allow Location Access"
              variant="primary"
              size="medium"
              loading={isLoading}
              onPress={handleLocationPermission}
              style={styles.permissionButton}
            />
          )}
        </Animated.View>

        {/* Notification Permission */}
        <Animated.View style={[styles.permissionCard, { backgroundColor: cardBackground, borderColor }, notificationStyle]}>
          <View style={styles.permissionHeader}>
            <View style={[styles.permissionIcon, { backgroundColor: `${tintColor}15` }]}>
              <IconSymbol name="bell.fill" size={24} color={tintColor} />
            </View>
            <View style={styles.permissionInfo}>
              <ThemedText type="defaultSemiBold" style={styles.permissionTitle}>
                Push Notifications
              </ThemedText>
              <View style={styles.permissionStatus}>
                <IconSymbol
                  name={notificationStatus.icon as any}
                  size={16}
                  color={notificationStatus.color}
                />
                <ThemedText style={[styles.permissionStatusText, { color: notificationStatus.color }]}>
                  {notificationStatus.text}
                </ThemedText>
              </View>
            </View>
          </View>

          <ThemedText style={[styles.permissionDescription, { color: mutedColor }]}>
            Stay updated with:
          </ThemedText>

          <View style={styles.permissionBenefits}>
            <View style={styles.benefitItem}>
              <IconSymbol name="checkmark" size={14} color={successColor} />
              <ThemedText style={styles.benefitText}>Order status updates</ThemedText>
            </View>
            <View style={styles.benefitItem}>
              <IconSymbol name="checkmark" size={14} color={successColor} />
              <ThemedText style={styles.benefitText}>Delivery notifications</ThemedText>
            </View>
            <View style={styles.benefitItem}>
              <IconSymbol name="checkmark" size={14} color={successColor} />
              <ThemedText style={styles.benefitText}>Special offers and promotions</ThemedText>
            </View>
          </View>

          {permissions.notifications.status !== 'granted' && (
            <Button
              title="Enable Notifications"
              variant="primary"
              size="medium"
              loading={isLoading}
              onPress={handleNotificationPermission}
              style={styles.permissionButton}
            />
          )}
        </Animated.View>

        {/* Error Message */}
        {error && (
          <View style={[styles.errorContainer, { backgroundColor: `${warningColor}15`, borderColor: `${warningColor}30` }]}>
            <IconSymbol name="exclamationmark.triangle" size={20} color={warningColor} />
            <ThemedText style={[styles.errorText, { color: warningColor }]}>
              {error}
            </ThemedText>
          </View>
        )}

        {/* Actions */}
        <Animated.View style={[styles.actions, actionsStyle]}>
          {permissions.location.status !== 'granted' || permissions.notifications.status !== 'granted' ? (
            <Button
              title="Allow All Permissions"
              variant="primary"
              size="large"
              loading={isLoading}
              onPress={handleAllPermissions}
              style={styles.primaryButton}
            />
          ) : (
            <View style={[styles.successContainer, { backgroundColor: `${successColor}15`, borderColor: `${successColor}30` }]}>
              <IconSymbol name="checkmark.circle.fill" size={24} color={successColor} />
              <ThemedText style={[styles.successText, { color: successColor }]}>
                All permissions granted! You&apos;re ready to go.
              </ThemedText>
            </View>
          )}

          <Button
            title="Skip for Now"
            variant="ghost"
            size="medium"
            onPress={onSkip}
            style={styles.skipButton}
          />

          <ThemedText style={[styles.skipNote, { color: mutedColor }]}>
            You can always enable these permissions later in Settings
          </ThemedText>
        </Animated.View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  permissionCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 20,
  },
  permissionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  permissionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  permissionInfo: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 18,
    marginBottom: 4,
  },
  permissionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  permissionStatusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  permissionDescription: {
    fontSize: 16,
    marginBottom: 12,
  },
  permissionBenefits: {
    gap: 8,
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  benefitText: {
    fontSize: 14,
    flex: 1,
  },
  permissionButton: {
    marginTop: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
    gap: 8,
  },
  errorText: {
    flex: 1,
    fontSize: 14,
  },
  actions: {
    alignItems: 'center',
    gap: 16,
  },
  primaryButton: {
    width: '100%',
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
    width: '100%',
  },
  successText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    width: '100%',
  },
  skipNote: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});
