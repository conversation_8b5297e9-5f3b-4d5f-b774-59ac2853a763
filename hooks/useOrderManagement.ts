/**
 * Hook for managing order operations (cancel, modify)
 */

import { useState, useCallback, useEffect } from 'react';
import { useNetworkStatus } from './useNetworkStatus';
import { orderApi, OrderApiError, mockOrderData } from '@/utils/orderApi';
import { 
  cacheOrder, 
  getCachedOrder, 
  storePendingAction, 
  getPendingActions,
  removePendingAction 
} from '@/utils/orderStorage';
import { 
  calculateOrderTimeConstraints, 
  calculateRefundAmount, 
  calculateModifiedPricing,
  validateModifications 
} from '@/utils/orderUtils';
import { 
  Order, 
  CancellationRequest, 
  ModificationRequest, 
  OrderActionResult,
  OrderTimeConstraints,
  OrderItemModification 
} from '@/types/order';

export interface UseOrderManagementState {
  order: Order | null;
  loading: boolean;
  error: string | null;
  timeConstraints: OrderTimeConstraints | null;
  refundAmount: number;
  isProcessing: boolean;
}

export interface UseOrderManagementReturn {
  state: UseOrderManagementState;
  actions: {
    loadOrder: (orderId: string) => Promise<void>;
    cancelOrder: (request: CancellationRequest) => Promise<OrderActionResult>;
    modifyOrder: (request: ModificationRequest) => Promise<OrderActionResult>;
    calculateModificationPricing: (modifications: OrderItemModification[]) => number;
    refreshOrder: () => Promise<void>;
    clearError: () => void;
  };
}

export function useOrderManagement(): UseOrderManagementReturn {
  const [state, setState] = useState<UseOrderManagementState>({
    order: null,
    loading: false,
    error: null,
    timeConstraints: null,
    refundAmount: 0,
    isProcessing: false,
  });

  const { isOnline } = useNetworkStatus();

  // Update time constraints and refund amount when order changes
  useEffect(() => {
    if (state.order) {
      const timeConstraints = calculateOrderTimeConstraints(state.order);
      const refundAmount = calculateRefundAmount(state.order);
      
      setState(prev => ({
        ...prev,
        timeConstraints,
        refundAmount,
      }));
    }
  }, [state.order]);

  const loadOrder = useCallback(async (orderId: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      let order: Order | null = null;

      if (isOnline) {
        // Try to fetch from API
        try {
          order = await orderApi.getOrder(orderId);
          // Cache the order for offline access
          await cacheOrder(order);
        } catch (error) {
          console.warn('Failed to fetch order from API, trying cache:', error);
          // Fall back to cached data
          const cachedOrder = await getCachedOrder(orderId);
          if (cachedOrder) {
            order = cachedOrder;
          }
        }
      } else {
        // Offline: use cached data
        const cachedOrder = await getCachedOrder(orderId);
        if (cachedOrder) {
          order = cachedOrder;
        }
      }

      // For development: use mock data if no order found
      if (!order && __DEV__) {
        order = { ...mockOrderData, id: orderId };
      }

      if (!order) {
        throw new Error('Order not found');
      }

      setState(prev => ({
        ...prev,
        order,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load order',
      }));
    }
  }, [isOnline]);

  const cancelOrder = useCallback(async (request: CancellationRequest): Promise<OrderActionResult> => {
    if (!state.order) {
      throw new Error('No order loaded');
    }

    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      let result: OrderActionResult;

      if (isOnline) {
        // Try to cancel via API
        result = await orderApi.cancelOrder(request);
        
        // Update cached order status
        const updatedOrder = { ...state.order, status: 'cancelled' as any };
        await cacheOrder(updatedOrder);
        
        setState(prev => ({
          ...prev,
          order: updatedOrder,
          isProcessing: false,
        }));
      } else {
        // Offline: store pending action
        await storePendingAction({
          type: 'cancel',
          orderId: request.orderId,
          data: request,
        });

        result = {
          success: true,
          message: 'Cancellation request saved. Will be processed when online.',
          refundAmount: calculateRefundAmount(state.order),
          estimatedProcessingTime: '1-2 business days',
        };

        setState(prev => ({ ...prev, isProcessing: false }));
      }

      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof OrderApiError 
          ? error.message 
          : 'Failed to cancel order',
      }));
      throw error;
    }
  }, [state.order, isOnline]);

  const modifyOrder = useCallback(async (request: ModificationRequest): Promise<OrderActionResult> => {
    if (!state.order) {
      throw new Error('No order loaded');
    }

    // Validate modifications
    const validation = validateModifications(state.order, request.modifications);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      let result: OrderActionResult;

      if (isOnline) {
        // Try to modify via API
        result = await orderApi.modifyOrder(request);
        
        // Calculate new pricing and update cached order
        const newPricing = calculateModifiedPricing(state.order, request.modifications);
        const updatedOrder = { ...state.order, pricing: newPricing };
        await cacheOrder(updatedOrder);
        
        setState(prev => ({
          ...prev,
          order: updatedOrder,
          isProcessing: false,
        }));
      } else {
        // Offline: store pending action
        await storePendingAction({
          type: 'modify',
          orderId: request.orderId,
          data: request,
        });

        const newPricing = calculateModifiedPricing(state.order, request.modifications);
        
        result = {
          success: true,
          message: 'Modification request saved. Will be processed when online.',
          newTotal: newPricing.total,
          estimatedProcessingTime: '1-2 business days',
        };

        setState(prev => ({ ...prev, isProcessing: false }));
      }

      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof OrderApiError 
          ? error.message 
          : 'Failed to modify order',
      }));
      throw error;
    }
  }, [state.order, isOnline]);

  const calculateModificationPricing = useCallback((modifications: OrderItemModification[]): number => {
    if (!state.order) return 0;
    
    const newPricing = calculateModifiedPricing(state.order, modifications);
    return newPricing.total;
  }, [state.order]);

  const refreshOrder = useCallback(async () => {
    if (state.order) {
      await loadOrder(state.order.id);
    }
  }, [state.order, loadOrder]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    state,
    actions: {
      loadOrder,
      cancelOrder,
      modifyOrder,
      calculateModificationPricing,
      refreshOrder,
      clearError,
    },
  };
}
