/**
 * Order management type definitions
 */

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PREPARING = 'preparing',
  READY = 'ready',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
}

export enum OrderItemModificationType {
  ADD = 'add',
  REMOVE = 'remove',
  UPDATE_QUANTITY = 'update_quantity',
  UPDATE_CUSTOMIZATION = 'update_customization',
}

export interface OrderItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  quantity: number;
  customizations?: OrderItemCustomization[];
  image?: string;
  category?: string;
  available?: boolean;
}

export interface OrderItemCustomization {
  id: string;
  name: string;
  value: string;
  price: number;
}

export interface OrderItemModification {
  type: OrderItemModificationType;
  itemId: string;
  newQuantity?: number;
  newCustomizations?: OrderItemCustomization[];
  newItem?: OrderItem;
}

export interface OrderPricing {
  subtotal: number;
  tax: number;
  deliveryFee: number;
  serviceFee: number;
  tip: number;
  discount: number;
  total: number;
}

export interface Order {
  id: string;
  restaurantId: string;
  restaurantName: string;
  restaurantImage?: string;
  status: OrderStatus;
  items: OrderItem[];
  pricing: OrderPricing;
  placedAt: Date;
  estimatedDeliveryTime?: Date;
  deliveryAddress: string;
  customerNotes?: string;
  cancellationPolicy: CancellationPolicy;
  modificationPolicy: ModificationPolicy;
}

export interface CancellationPolicy {
  allowedUntilMinutes: number; // Minutes after order placement
  refundPercentage: number; // 0-100
  cancellationFee: number;
  reasons: string[];
}

export interface ModificationPolicy {
  allowedUntilMinutes: number;
  allowAddItems: boolean;
  allowRemoveItems: boolean;
  allowQuantityChanges: boolean;
  allowCustomizationChanges: boolean;
}

export interface CancellationRequest {
  orderId: string;
  reason: string;
  additionalNotes?: string;
}

export interface ModificationRequest {
  orderId: string;
  modifications: OrderItemModification[];
  reason?: string;
}

export interface OrderActionResult {
  success: boolean;
  message: string;
  refundAmount?: number;
  newTotal?: number;
  estimatedProcessingTime?: string;
}

export interface OrderTimeConstraints {
  canCancel: boolean;
  canModify: boolean;
  timeRemaining: {
    cancellation: number; // minutes
    modification: number; // minutes
  };
}
