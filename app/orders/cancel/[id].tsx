/**
 * Order cancellation page
 */

import { ConfirmationDialog } from '@/components/orders/ConfirmationDialog';
import { OrderDetailsCard } from '@/components/orders/OrderDetailsCard';
import { OrderItemsList } from '@/components/orders/OrderItemsList';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useOrderManagement } from '@/hooks/useOrderManagement';
import { useThemeColor } from '@/hooks/useThemeColor';
import { CancellationRequest } from '@/types/order';
import { formatCurrency, formatTimeRemaining } from '@/utils/orderUtils';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    View
} from 'react-native';

export default function OrderCancellationPage() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { state, actions } = useOrderManagement();
  const { isOnline } = useNetworkStatus();

  const [selectedReason, setSelectedReason] = useState<string>('');
  const [additionalNotes, setAdditionalNotes] = useState<string>('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const borderColor = useThemeColor({}, 'border');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const mutedColor = useThemeColor({}, 'muted');
  const tintColor = useThemeColor({}, 'tint');
  const errorColor = useThemeColor({}, 'error');
  const warningColor = useThemeColor({}, 'warning');

  useEffect(() => {
    if (id) {
      actions.loadOrder(id);
    }
  }, [id]);

  const handleReasonSelect = (reason: string) => {
    setSelectedReason(reason);
  };

  const handleCancel = () => {
    if (!selectedReason) {
      Alert.alert('Please select a reason', 'You must select a reason for cancellation.');
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirmCancel = async () => {
    if (!state.order || !selectedReason) return;

    setIsSubmitting(true);

    try {
      const request: CancellationRequest = {
        orderId: state.order.id,
        reason: selectedReason,
        additionalNotes: additionalNotes || undefined,
      };

      const result = await actions.cancelOrder(request);

      setShowConfirmation(false);

      Alert.alert(
        'Cancellation Successful',
        result.message,
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      setShowConfirmation(false);
      Alert.alert(
        'Cancellation Failed',
        error instanceof Error ? error.message : 'Failed to cancel order. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (state.loading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingSpinner message="Loading order details..." />
      </ThemedView>
    );
  }

  if (state.error) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={48} color={errorColor} />
          <ThemedText type="subtitle" style={styles.errorTitle}>
            Error Loading Order
          </ThemedText>
          <ThemedText style={[styles.errorMessage, { color: mutedColor }]}>
            {state.error}
          </ThemedText>
          <Button
            title="Try Again"
            onPress={() => id && actions.loadOrder(id)}
            style={styles.retryButton}
          />
        </View>
      </ThemedView>
    );
  }

  if (!state.order || !state.timeConstraints) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="questionmark.circle" size={48} color={mutedColor} />
          <ThemedText type="subtitle" style={styles.errorTitle}>
            Order Not Found
          </ThemedText>
          <ThemedText style={[styles.errorMessage, { color: mutedColor }]}>
            The order you're looking for could not be found.
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  const canCancel = state.timeConstraints.canCancel;
  const timeRemaining = state.timeConstraints.timeRemaining.cancellation;

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Cancel Order',
          headerTintColor: errorColor,
        }}
      />

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Network Status Warning */}
          {!isOnline && (
            <View style={[styles.offlineWarning, { backgroundColor: `${warningColor}15`, borderColor: `${warningColor}40` }]}>
              <IconSymbol name="wifi.slash" size={16} color={warningColor} />
              <ThemedText style={[styles.offlineText, { color: warningColor }]}>
                You're offline. Cancellation will be processed when connection is restored.
              </ThemedText>
            </View>
          )}

          {/* Order Details */}
          <OrderDetailsCard
            order={state.order}
            showTimeConstraints
            timeRemaining={state.timeConstraints.timeRemaining}
          />

          {/* Order Items */}
          <OrderItemsList items={state.order.items} />

          {/* Cancellation Status */}
          {!canCancel ? (
            <View style={[styles.statusCard, { backgroundColor: `${errorColor}15`, borderColor: `${errorColor}40` }]}>
              <IconSymbol name="clock.badge.xmark" size={24} color={errorColor} />
              <View style={styles.statusContent}>
                <ThemedText type="defaultSemiBold" style={styles.statusTitle}>
                  Cancellation Not Available
                </ThemedText>
                <ThemedText style={[styles.statusMessage, { color: mutedColor }]}>
                  The cancellation window has expired. Contact customer support for assistance.
                </ThemedText>
              </View>
            </View>
          ) : (
            <>
              {/* Time Warning */}
              {timeRemaining <= 5 && (
                <View style={[styles.statusCard, { backgroundColor: `${warningColor}15`, borderColor: `${warningColor}40` }]}>
                  <IconSymbol name="clock" size={24} color={warningColor} />
                  <View style={styles.statusContent}>
                    <ThemedText type="defaultSemiBold" style={styles.statusTitle}>
                      Limited Time Remaining
                    </ThemedText>
                    <ThemedText style={[styles.statusMessage, { color: mutedColor }]}>
                      You have {formatTimeRemaining(timeRemaining)} left to cancel this order.
                    </ThemedText>
                  </View>
                </View>
              )}

              {/* Cancellation Policy */}
              <View style={[styles.policyCard, { backgroundColor: cardBackground, borderColor }]}>
                <ThemedText type="defaultSemiBold" style={styles.policyTitle}>
                  Cancellation Policy
                </ThemedText>
                <View style={styles.policyDetails}>
                  <View style={styles.policyRow}>
                    <ThemedText style={styles.policyLabel}>Refund Amount:</ThemedText>
                    <ThemedText type="defaultSemiBold" style={styles.policyValue}>
                      {formatCurrency(state.refundAmount)}
                    </ThemedText>
                  </View>
                  <View style={styles.policyRow}>
                    <ThemedText style={styles.policyLabel}>Cancellation Fee:</ThemedText>
                    <ThemedText style={styles.policyValue}>
                      {formatCurrency(state.order.cancellationPolicy.cancellationFee)}
                    </ThemedText>
                  </View>
                  <View style={styles.policyRow}>
                    <ThemedText style={styles.policyLabel}>Processing Time:</ThemedText>
                    <ThemedText style={[styles.policyValue, { color: mutedColor }]}>
                      3-5 business days
                    </ThemedText>
                  </View>
                </View>
              </View>

              {/* Cancellation Reasons */}
              <View style={[styles.reasonsCard, { backgroundColor: cardBackground, borderColor }]}>
                <ThemedText type="defaultSemiBold" style={styles.reasonsTitle}>
                  Reason for Cancellation *
                </ThemedText>
                {state.order.cancellationPolicy.reasons.map((reason) => (
                  <Button
                    key={reason}
                    title={reason}
                    variant={selectedReason === reason ? 'primary' : 'outline'}
                    onPress={() => handleReasonSelect(reason)}
                    style={styles.reasonButton}
                    leftIcon={
                      selectedReason === reason ? (
                        <IconSymbol name="checkmark.circle.fill" size={16} color="white" />
                      ) : (
                        <IconSymbol name="circle" size={16} color={tintColor} />
                      )
                    }
                  />
                ))}
              </View>
            </>
          )}
        </ScrollView>

        {/* Action Buttons */}
        {canCancel && (
          <View style={[styles.actionBar, { borderTopColor: borderColor }]}>
            <Button
              title="Keep Order"
              variant="outline"
              onPress={() => router.back()}
              style={styles.actionButton}
            />
            <Button
              title="Cancel Order"
              variant="danger"
              onPress={handleCancel}
              style={styles.actionButton}
              disabled={!selectedReason}
            />
          </View>
        )}
      </KeyboardAvoidingView>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        visible={showConfirmation}
        type="cancel"
        title="Cancel Order?"
        message="Are you sure you want to cancel this order? This action cannot be undone."
        confirmText="Yes, Cancel Order"
        cancelText="Keep Order"
        onConfirm={handleConfirmCancel}
        onCancel={() => setShowConfirmation(false)}
        loading={isSubmitting}
        details={{
          refundAmount: state.refundAmount,
          processingTime: isOnline ? '3-5 business days' : 'When connection is restored',
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 120,
  },
  offlineWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  offlineText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  statusCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  statusContent: {
    marginLeft: 12,
    flex: 1,
  },
  statusTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  statusMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  policyCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  policyTitle: {
    fontSize: 16,
    marginBottom: 12,
  },
  policyDetails: {
    gap: 8,
  },
  policyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  policyLabel: {
    fontSize: 14,
    flex: 1,
  },
  policyValue: {
    fontSize: 14,
  },
  reasonsCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  reasonsTitle: {
    fontSize: 16,
    marginBottom: 12,
  },
  reasonButton: {
    marginBottom: 8,
    justifyContent: 'flex-start',
  },
  actionBar: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});
