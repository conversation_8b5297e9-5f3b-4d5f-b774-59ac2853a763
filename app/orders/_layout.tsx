/**
 * Orders stack layout for order management pages
 */

import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function OrdersLayout() {
  return (
    <>
      <Stack
        screenOptions={{
          headerShown: true,
          headerStyle: {
            backgroundColor: 'transparent',
          },
          headerTintColor: '#000',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen 
          name="cancel/[id]" 
          options={{
            title: 'Cancel Order',
            presentation: 'modal',
          }}
        />
        <Stack.Screen 
          name="modify/[id]" 
          options={{
            title: 'Modify Order',
            presentation: 'modal',
          }}
        />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}
