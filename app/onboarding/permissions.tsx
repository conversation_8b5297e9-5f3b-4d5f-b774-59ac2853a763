/**
 * Permissions onboarding screen
 */

import { OnboardingLayout } from '@/components/onboarding/OnboardingLayout';
import { PermissionsScreen } from '@/components/onboarding/PermissionsScreen';
import { useOnboarding } from '@/hooks/useOnboarding';
import React from 'react';

export default function PermissionsOnboardingScreen() {
  const { completeOnboarding } = useOnboarding();

  const handlePermissionsGranted = async (permissions: { location: boolean; notifications: boolean }) => {
    console.log('Permissions granted:', permissions);
    // Complete onboarding regardless of permission status
    await completeOnboarding();
  };

  const handleSkip = async () => {
    // Complete onboarding even if permissions are skipped
    await completeOnboarding();
  };

  return (
    <OnboardingLayout
      showSkip={false}
      showNext={false}
      customActions={null}
    >
      <PermissionsScreen
        onPermissionsGranted={handlePermissionsGranted}
        onSkip={handleSkip}
      />
    </OnboardingLayout>
  );
}
