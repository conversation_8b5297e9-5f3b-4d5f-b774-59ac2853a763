/**
 * Order storage utilities for caching and offline support
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Order } from '@/types/order';

// Storage keys
const ORDER_STORAGE_KEYS = {
  CACHED_ORDERS: '@foodapp/cached_orders',
  ORDER_HISTORY: '@foodapp/order_history',
  PENDING_ACTIONS: '@foodapp/pending_actions',
  LAST_SYNC: '@foodapp/last_sync',
} as const;

export interface CachedOrder extends Order {
  cachedAt: Date;
  lastUpdated: Date;
}

export interface PendingOrderAction {
  id: string;
  type: 'cancel' | 'modify';
  orderId: string;
  data: any;
  timestamp: Date;
  retryCount: number;
}

/**
 * Cache an order for offline access
 */
export async function cacheOrder(order: Order): Promise<void> {
  try {
    const cachedOrders = await getCachedOrders();
    const now = new Date();
    
    const cachedOrder: CachedOrder = {
      ...order,
      cachedAt: now,
      lastUpdated: now,
    };

    // Update or add the order
    const existingIndex = cachedOrders.findIndex(o => o.id === order.id);
    if (existingIndex !== -1) {
      cachedOrders[existingIndex] = cachedOrder;
    } else {
      cachedOrders.push(cachedOrder);
    }

    // Keep only the last 50 orders to manage storage
    const sortedOrders = cachedOrders
      .sort((a, b) => new Date(b.placedAt).getTime() - new Date(a.placedAt).getTime())
      .slice(0, 50);

    await AsyncStorage.setItem(
      ORDER_STORAGE_KEYS.CACHED_ORDERS,
      JSON.stringify(sortedOrders)
    );
  } catch (error) {
    console.error('Error caching order:', error);
  }
}

/**
 * Get cached orders
 */
export async function getCachedOrders(): Promise<CachedOrder[]> {
  try {
    const cached = await AsyncStorage.getItem(ORDER_STORAGE_KEYS.CACHED_ORDERS);
    if (!cached) return [];

    const orders = JSON.parse(cached);
    return orders.map((order: any) => ({
      ...order,
      placedAt: new Date(order.placedAt),
      estimatedDeliveryTime: order.estimatedDeliveryTime 
        ? new Date(order.estimatedDeliveryTime) 
        : undefined,
      cachedAt: new Date(order.cachedAt),
      lastUpdated: new Date(order.lastUpdated),
    }));
  } catch (error) {
    console.error('Error getting cached orders:', error);
    return [];
  }
}

/**
 * Get a specific cached order
 */
export async function getCachedOrder(orderId: string): Promise<CachedOrder | null> {
  try {
    const cachedOrders = await getCachedOrders();
    return cachedOrders.find(order => order.id === orderId) || null;
  } catch (error) {
    console.error('Error getting cached order:', error);
    return null;
  }
}

/**
 * Remove an order from cache
 */
export async function removeCachedOrder(orderId: string): Promise<void> {
  try {
    const cachedOrders = await getCachedOrders();
    const filteredOrders = cachedOrders.filter(order => order.id !== orderId);
    
    await AsyncStorage.setItem(
      ORDER_STORAGE_KEYS.CACHED_ORDERS,
      JSON.stringify(filteredOrders)
    );
  } catch (error) {
    console.error('Error removing cached order:', error);
  }
}

/**
 * Store a pending action for offline sync
 */
export async function storePendingAction(action: Omit<PendingOrderAction, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
  try {
    const pendingActions = await getPendingActions();
    
    const newAction: PendingOrderAction = {
      ...action,
      id: `${action.type}_${action.orderId}_${Date.now()}`,
      timestamp: new Date(),
      retryCount: 0,
    };

    pendingActions.push(newAction);
    
    await AsyncStorage.setItem(
      ORDER_STORAGE_KEYS.PENDING_ACTIONS,
      JSON.stringify(pendingActions)
    );
  } catch (error) {
    console.error('Error storing pending action:', error);
  }
}

/**
 * Get pending actions
 */
export async function getPendingActions(): Promise<PendingOrderAction[]> {
  try {
    const pending = await AsyncStorage.getItem(ORDER_STORAGE_KEYS.PENDING_ACTIONS);
    if (!pending) return [];

    const actions = JSON.parse(pending);
    return actions.map((action: any) => ({
      ...action,
      timestamp: new Date(action.timestamp),
    }));
  } catch (error) {
    console.error('Error getting pending actions:', error);
    return [];
  }
}

/**
 * Remove a pending action
 */
export async function removePendingAction(actionId: string): Promise<void> {
  try {
    const pendingActions = await getPendingActions();
    const filteredActions = pendingActions.filter(action => action.id !== actionId);
    
    await AsyncStorage.setItem(
      ORDER_STORAGE_KEYS.PENDING_ACTIONS,
      JSON.stringify(filteredActions)
    );
  } catch (error) {
    console.error('Error removing pending action:', error);
  }
}

/**
 * Update retry count for a pending action
 */
export async function updatePendingActionRetryCount(actionId: string, retryCount: number): Promise<void> {
  try {
    const pendingActions = await getPendingActions();
    const actionIndex = pendingActions.findIndex(action => action.id === actionId);
    
    if (actionIndex !== -1) {
      pendingActions[actionIndex].retryCount = retryCount;
      
      await AsyncStorage.setItem(
        ORDER_STORAGE_KEYS.PENDING_ACTIONS,
        JSON.stringify(pendingActions)
      );
    }
  } catch (error) {
    console.error('Error updating pending action retry count:', error);
  }
}

/**
 * Clear all cached data
 */
export async function clearOrderCache(): Promise<void> {
  try {
    await Promise.all([
      AsyncStorage.removeItem(ORDER_STORAGE_KEYS.CACHED_ORDERS),
      AsyncStorage.removeItem(ORDER_STORAGE_KEYS.ORDER_HISTORY),
      AsyncStorage.removeItem(ORDER_STORAGE_KEYS.PENDING_ACTIONS),
      AsyncStorage.removeItem(ORDER_STORAGE_KEYS.LAST_SYNC),
    ]);
  } catch (error) {
    console.error('Error clearing order cache:', error);
  }
}

/**
 * Get last sync timestamp
 */
export async function getLastSyncTime(): Promise<Date | null> {
  try {
    const lastSync = await AsyncStorage.getItem(ORDER_STORAGE_KEYS.LAST_SYNC);
    return lastSync ? new Date(lastSync) : null;
  } catch (error) {
    console.error('Error getting last sync time:', error);
    return null;
  }
}

/**
 * Update last sync timestamp
 */
export async function updateLastSyncTime(): Promise<void> {
  try {
    await AsyncStorage.setItem(
      ORDER_STORAGE_KEYS.LAST_SYNC,
      new Date().toISOString()
    );
  } catch (error) {
    console.error('Error updating last sync time:', error);
  }
}

/**
 * Check if cached data is stale
 */
export function isCacheStale(cachedAt: Date, maxAgeMinutes: number = 30): boolean {
  const now = new Date();
  const ageMinutes = (now.getTime() - cachedAt.getTime()) / (1000 * 60);
  return ageMinutes > maxAgeMinutes;
}
