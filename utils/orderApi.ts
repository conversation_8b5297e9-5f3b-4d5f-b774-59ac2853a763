/**
 * Order API utilities with retry and error handling
 */

import { retryUtils } from './retryUtils';
import { logApiError } from './errorLogger';
import { 
  Order, 
  CancellationRequest, 
  ModificationRequest, 
  OrderActionResult 
} from '@/types/order';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'https://api.foodapp.com';

class OrderApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'OrderApiError';
  }
}

/**
 * Order API client with retry logic and error handling
 */
export class OrderApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Fetch order details by ID
   */
  async getOrder(orderId: string): Promise<Order> {
    const result = await retryUtils.retryFetch(
      `${this.baseUrl}/orders/${orderId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers here
        },
      },
      {
        maxAttempts: 3,
        baseDelay: 1000,
      }
    );

    if (!result.success) {
      logApiError(`/orders/${orderId}`, 0, result.error?.message || 'Failed to fetch order');
      throw new OrderApiError('Failed to fetch order details', 0);
    }

    const response = result.data!;
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      logApiError(`/orders/${orderId}`, response.status, errorData.message);
      throw new OrderApiError(
        errorData.message || 'Failed to fetch order',
        response.status,
        errorData.code
      );
    }

    const orderData = await response.json();
    return {
      ...orderData,
      placedAt: new Date(orderData.placedAt),
      estimatedDeliveryTime: orderData.estimatedDeliveryTime 
        ? new Date(orderData.estimatedDeliveryTime) 
        : undefined,
    };
  }

  /**
   * Cancel an order
   */
  async cancelOrder(request: CancellationRequest): Promise<OrderActionResult> {
    const result = await retryUtils.retryFetch(
      `${this.baseUrl}/orders/${request.orderId}/cancel`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: request.reason,
          additionalNotes: request.additionalNotes,
        }),
      },
      {
        maxAttempts: 2, // Fewer retries for cancellation to avoid double-cancellation
        baseDelay: 1000,
        retryCondition: (error: any) => {
          // Don't retry client errors (4xx)
          return error.status >= 500;
        },
      }
    );

    if (!result.success) {
      logApiError(`/orders/${request.orderId}/cancel`, 0, result.error?.message || 'Failed to cancel order');
      throw new OrderApiError('Failed to cancel order', 0);
    }

    const response = result.data!;
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      logApiError(`/orders/${request.orderId}/cancel`, response.status, errorData.message);
      throw new OrderApiError(
        errorData.message || 'Failed to cancel order',
        response.status,
        errorData.code
      );
    }

    return await response.json();
  }

  /**
   * Modify an order
   */
  async modifyOrder(request: ModificationRequest): Promise<OrderActionResult> {
    const result = await retryUtils.retryFetch(
      `${this.baseUrl}/orders/${request.orderId}/modify`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modifications: request.modifications,
          reason: request.reason,
        }),
      },
      {
        maxAttempts: 2,
        baseDelay: 1000,
        retryCondition: (error: any) => {
          return error.status >= 500;
        },
      }
    );

    if (!result.success) {
      logApiError(`/orders/${request.orderId}/modify`, 0, result.error?.message || 'Failed to modify order');
      throw new OrderApiError('Failed to modify order', 0);
    }

    const response = result.data!;
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      logApiError(`/orders/${request.orderId}/modify`, response.status, errorData.message);
      throw new OrderApiError(
        errorData.message || 'Failed to modify order',
        response.status,
        errorData.code
      );
    }

    return await response.json();
  }

  /**
   * Get available menu items for modification
   */
  async getAvailableItems(restaurantId: string): Promise<any[]> {
    const result = await retryUtils.retryFetch(
      `${this.baseUrl}/restaurants/${restaurantId}/menu`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
      {
        maxAttempts: 3,
        baseDelay: 1000,
      }
    );

    if (!result.success) {
      logApiError(`/restaurants/${restaurantId}/menu`, 0, result.error?.message || 'Failed to fetch menu');
      throw new OrderApiError('Failed to fetch menu items', 0);
    }

    const response = result.data!;
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      logApiError(`/restaurants/${restaurantId}/menu`, response.status, errorData.message);
      throw new OrderApiError(
        errorData.message || 'Failed to fetch menu',
        response.status,
        errorData.code
      );
    }

    return await response.json();
  }
}

// Export singleton instance
export const orderApi = new OrderApiClient();

// Export error class for type checking
export { OrderApiError };

/**
 * Mock data for development/testing
 */
export const mockOrderData: Order = {
  id: '12345',
  restaurantId: 'rest-001',
  restaurantName: "Mario's Pizza",
  restaurantImage: 'https://example.com/mario-pizza.jpg',
  status: 'confirmed' as any,
  items: [
    {
      id: 'item-1',
      name: 'Margherita Pizza',
      description: 'Fresh tomatoes, mozzarella, basil',
      price: 18.99,
      quantity: 1,
      customizations: [
        { id: 'cust-1', name: 'Size', value: 'Large', price: 3.00 },
        { id: 'cust-2', name: 'Extra Cheese', value: 'Yes', price: 2.50 },
      ],
      image: 'https://example.com/margherita.jpg',
      category: 'Pizza',
      available: true,
    },
    {
      id: 'item-2',
      name: 'Caesar Salad',
      description: 'Romaine lettuce, parmesan, croutons',
      price: 12.99,
      quantity: 1,
      customizations: [],
      image: 'https://example.com/caesar.jpg',
      category: 'Salad',
      available: true,
    },
  ],
  pricing: {
    subtotal: 37.48,
    tax: 3.37,
    deliveryFee: 2.99,
    serviceFee: 1.50,
    tip: 7.50,
    discount: 0,
    total: 52.84,
  },
  placedAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
  estimatedDeliveryTime: new Date(Date.now() + 25 * 60 * 1000), // 25 minutes from now
  deliveryAddress: '123 Main St, Apt 4B, New York, NY 10001',
  customerNotes: 'Please ring doorbell',
  cancellationPolicy: {
    allowedUntilMinutes: 15,
    refundPercentage: 100,
    cancellationFee: 0,
    reasons: [
      'Changed my mind',
      'Ordered by mistake',
      'Found a better deal',
      'Emergency came up',
      'Other',
    ],
  },
  modificationPolicy: {
    allowedUntilMinutes: 10,
    allowAddItems: true,
    allowRemoveItems: true,
    allowQuantityChanges: true,
    allowCustomizationChanges: false,
  },
};
