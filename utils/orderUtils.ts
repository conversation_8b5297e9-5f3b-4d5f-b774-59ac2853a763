/**
 * Order management utilities
 */

import { 
  Order, 
  OrderStatus, 
  OrderTimeConstraints, 
  OrderPricing, 
  OrderItem, 
  OrderItemModification,
  OrderItemModificationType 
} from '@/types/order';

/**
 * Calculate time constraints for order actions
 */
export function calculateOrderTimeConstraints(order: Order): OrderTimeConstraints {
  const now = new Date();
  const orderTime = new Date(order.placedAt);
  const minutesSinceOrder = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));

  const cancellationTimeRemaining = Math.max(0, order.cancellationPolicy.allowedUntilMinutes - minutesSinceOrder);
  const modificationTimeRemaining = Math.max(0, order.modificationPolicy.allowedUntilMinutes - minutesSinceOrder);

  const canCancel = cancellationTimeRemaining > 0 && 
    [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status);
  
  const canModify = modificationTimeRemaining > 0 && 
    [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status);

  return {
    canCancel,
    canModify,
    timeRemaining: {
      cancellation: cancellationTimeRemaining,
      modification: modificationTimeRemaining,
    },
  };
}

/**
 * Calculate refund amount for order cancellation
 */
export function calculateRefundAmount(order: Order): number {
  const refundPercentage = order.cancellationPolicy.refundPercentage / 100;
  const refundableAmount = order.pricing.total - order.cancellationPolicy.cancellationFee;
  return Math.max(0, refundableAmount * refundPercentage);
}

/**
 * Calculate new pricing after modifications
 */
export function calculateModifiedPricing(
  originalOrder: Order, 
  modifications: OrderItemModification[]
): OrderPricing {
  let newItems = [...originalOrder.items];

  // Apply modifications
  modifications.forEach(mod => {
    switch (mod.type) {
      case OrderItemModificationType.ADD:
        if (mod.newItem) {
          newItems.push(mod.newItem);
        }
        break;
      
      case OrderItemModificationType.REMOVE:
        newItems = newItems.filter(item => item.id !== mod.itemId);
        break;
      
      case OrderItemModificationType.UPDATE_QUANTITY:
        const itemIndex = newItems.findIndex(item => item.id === mod.itemId);
        if (itemIndex !== -1 && mod.newQuantity !== undefined) {
          if (mod.newQuantity === 0) {
            newItems.splice(itemIndex, 1);
          } else {
            newItems[itemIndex] = { ...newItems[itemIndex], quantity: mod.newQuantity };
          }
        }
        break;
      
      case OrderItemModificationType.UPDATE_CUSTOMIZATION:
        const customItemIndex = newItems.findIndex(item => item.id === mod.itemId);
        if (customItemIndex !== -1 && mod.newCustomizations) {
          newItems[customItemIndex] = { 
            ...newItems[customItemIndex], 
            customizations: mod.newCustomizations 
          };
        }
        break;
    }
  });

  // Calculate new subtotal
  const subtotal = newItems.reduce((total, item) => {
    const itemPrice = item.price * item.quantity;
    const customizationPrice = (item.customizations || []).reduce(
      (sum, custom) => sum + (custom.price * item.quantity), 0
    );
    return total + itemPrice + customizationPrice;
  }, 0);

  // Keep original fees and tax rate
  const taxRate = originalOrder.pricing.tax / originalOrder.pricing.subtotal;
  const tax = subtotal * taxRate;

  return {
    subtotal,
    tax,
    deliveryFee: originalOrder.pricing.deliveryFee,
    serviceFee: originalOrder.pricing.serviceFee,
    tip: originalOrder.pricing.tip,
    discount: originalOrder.pricing.discount,
    total: subtotal + tax + originalOrder.pricing.deliveryFee + 
           originalOrder.pricing.serviceFee + originalOrder.pricing.tip - 
           originalOrder.pricing.discount,
  };
}

/**
 * Format time remaining as human-readable string
 */
export function formatTimeRemaining(minutes: number): string {
  if (minutes <= 0) return 'Expired';
  
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
}

/**
 * Format currency amount
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

/**
 * Get order status display information
 */
export function getOrderStatusInfo(status: OrderStatus): {
  label: string;
  color: string;
  icon: string;
} {
  switch (status) {
    case OrderStatus.PENDING:
      return { label: 'Pending', color: '#D97706', icon: 'clock' };
    case OrderStatus.CONFIRMED:
      return { label: 'Confirmed', color: '#059669', icon: 'checkmark.circle' };
    case OrderStatus.PREPARING:
      return { label: 'Preparing', color: '#DC2626', icon: 'flame' };
    case OrderStatus.READY:
      return { label: 'Ready', color: '#059669', icon: 'checkmark.circle.fill' };
    case OrderStatus.OUT_FOR_DELIVERY:
      return { label: 'Out for Delivery', color: '#0369A1', icon: 'car' };
    case OrderStatus.DELIVERED:
      return { label: 'Delivered', color: '#059669', icon: 'checkmark.seal.fill' };
    case OrderStatus.CANCELLED:
      return { label: 'Cancelled', color: '#DC2626', icon: 'xmark.circle' };
    default:
      return { label: 'Unknown', color: '#6B7280', icon: 'questionmark.circle' };
  }
}

/**
 * Validate modification request
 */
export function validateModifications(
  order: Order, 
  modifications: OrderItemModification[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const constraints = calculateOrderTimeConstraints(order);

  if (!constraints.canModify) {
    errors.push('Order can no longer be modified');
    return { isValid: false, errors };
  }

  // Check policy constraints
  modifications.forEach(mod => {
    switch (mod.type) {
      case OrderItemModificationType.ADD:
        if (!order.modificationPolicy.allowAddItems) {
          errors.push('Adding items is not allowed for this order');
        }
        break;
      
      case OrderItemModificationType.REMOVE:
        if (!order.modificationPolicy.allowRemoveItems) {
          errors.push('Removing items is not allowed for this order');
        }
        break;
      
      case OrderItemModificationType.UPDATE_QUANTITY:
        if (!order.modificationPolicy.allowQuantityChanges) {
          errors.push('Quantity changes are not allowed for this order');
        }
        break;
      
      case OrderItemModificationType.UPDATE_CUSTOMIZATION:
        if (!order.modificationPolicy.allowCustomizationChanges) {
          errors.push('Customization changes are not allowed for this order');
        }
        break;
    }
  });

  return { isValid: errors.length === 0, errors };
}
